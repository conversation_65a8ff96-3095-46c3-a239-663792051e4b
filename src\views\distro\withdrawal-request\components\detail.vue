<template>
    <ele-drawer v-model="visible" title="产品名称" :size="600" style="max-width: 100%;" @close="close">
        <el-row :gutter="10" class="mb-20">
            <el-col :span="4">
                <div class="grid-content ep-bg-purple font-16 font-weight-500">产品ID：</div>
            </el-col>
            <el-col :span="8">
                <div class="grid-content ep-bg-purple">wuja</div>
            </el-col>
            <el-col :span="4">
                <div class="grid-content ep-bg-purple font-16 font-weight-500">产品名称：</div>
            </el-col>
            <el-col :span="8">
                <div class="grid-content ep-bg-purple">34</div>
            </el-col>
        </el-row>

        <el-row :gutter="10" class="mb-20">
            <el-col :span="8">
                <div class="grid-content ep-bg-purple font-16 font-weight-500">产品描述：</div>
            </el-col>
            <el-col :span="16">
                <div class="grid-content ep-bg-purple">34</div>
            </el-col>
        </el-row>

        <el-row :gutter="10" class="mb-20">
            <el-col :span="8">
                <div class="grid-content ep-bg-purple font-16 font-weight-500">关联佣金规则ID：</div>
            </el-col>
            <el-col :span="16">
                <div class="grid-content ep-bg-purple">34</div>
            </el-col>
        </el-row>

        <el-row :gutter="10" class="mb-20">
            <el-col :span="8">
                <div class="grid-content ep-bg-purple font-16 font-weight-500">关联佣金规则名称：</div>
            </el-col>
            <el-col :span="16">
                <div class="grid-content ep-bg-purple">34</div>
            </el-col>
        </el-row>

        <el-row :gutter="10" class="mb-20">
            <el-col :span="8">
                <div class="grid-content ep-bg-purple font-16 font-weight-500">启用状态：</div>
            </el-col>
            <el-col :span="16">
                <div class="grid-content ep-bg-purple">34</div>
            </el-col>
        </el-row>

        <el-row :gutter="10" class="mb-20">
            <el-col :span="4">
                <div class="grid-content ep-bg-purple font-16 font-weight-500">创建人ID：</div>
            </el-col>
            <el-col :span="8">
                <div class="grid-content ep-bg-purple">34</div>
            </el-col>
            <el-col :span="4">
                <div class="grid-content ep-bg-purple font-16 font-weight-500">更新人ID：</div>
            </el-col>
            <el-col :span="8">
                <div class="grid-content ep-bg-purple">34</div>
            </el-col>
        </el-row>

        <el-row :gutter="10" class="mb-20">
            <el-col :span="4">
                <div class="grid-content ep-bg-purple font-16 font-weight-500">创建时间：</div>
            </el-col>
            <el-col :span="8">
                <div class="grid-content ep-bg-purple">34</div>
            </el-col>
            <el-col :span="4">
                <div class="grid-content ep-bg-purple font-16 font-weight-500">更新时间：</div>
            </el-col>
            <el-col :span="8">
                <div class="grid-content ep-bg-purple">34</div>
            </el-col>
        </el-row>
    </ele-drawer>
</template>

<script setup>
import { defineProps, defineEmits, computed } from 'vue'

const props = defineProps({
    visible: { type: Boolean, default: false },
    data: { type: Object, default: () => ({}) }
})

const emit = defineEmits(['close'])

const visible = computed({
    get() { return props.visible },
    set() { }
})

function close() {
    emit('close');
}
</script>