<template>
  <ele-page>
    <!-- 搜索表单 -->
    <Search @search="reload" />
    <ele-card :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table ref="tableRef" row-key="productId" :columns="columns" :datasource="withDrawalList"
        :pagination="true" :show-overflow-tooltip="false">

        <template #action="{ row }">
          <template v-if="row.username !== 'admin'">
            <el-link v-permission="'system:user:remove'" type="primary" :underline="false" @click="showBatch(row)">
              查看
            </el-link>
            <el-divider v-permission="['system:user:edit', 'system:user:remove']" direction="vertical" />
            <el-link v-permission="'system:user:edit'" type="primary" :underline="false" @click="openEdit(row)">
              修改
            </el-link>
            <el-divider v-permission="['system:user:edit', 'system:user:remove']" direction="vertical" />
            <el-link v-permission="'system:user:remove'" type="danger" :underline="false">
              删除
            </el-link>
          </template>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <user-edit v-model="showEdit" :data="current" @done="reload" />
    <!-- 详情内容 - 抽屉 -->
    <Detail :visible="showDetail" @close="onClose" />
  </ele-page>
</template>

<script setup>
import { ref } from 'vue';
import Search from './components/search.vue';
import UserEdit from './components/user-edit.vue';
import Detail from './components/detail.vue';
import {
  requestWithdrawalRequestList,
  //  requestWithdrawalRequestAdminList 
} from '@/api/distro/withdrawal-request/index';

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = ref([
  {
    type: 'index',
    columnKey: 'index',
    width: 50,
    align: 'center',
    fixed: 'left'
  },
  {
    prop: 'productId',
    label: '申请ID',
    align: 'center',
    minWidth: 110
  },
  {
    prop: 'productName',
    label: '申请金额',
    align: 'center',
    minWidth: 200,
    slot: 'roleList'
  },
  {
    prop: 'createTime',
    label: '申请时间',
    align: 'center',
    minWidth: 110,
  },
  {
    prop: 'isActive',
    label: '状态',
    align: 'center',
    minWidth: 60,
    slot: 'isActive'
  },
  {
    prop: 'createTime',
    label: '完成时间',
    align: 'center',
    minWidth: 110,
  },
  {
    prop: 'createTime',
    label: '银行流水号',
    align: 'center',
    minWidth: 110,
  },
  {
    prop: 'createTime',
    label: '拒绝理由',
    align: 'center',
    minWidth: 110,
  },
  {
    columnKey: 'action',
    label: '操作',
    width: 180,
    align: 'center',
    slot: 'action',
    fixed: 'right',
    hideInPrint: true,
    hideInExport: true
  }
]);

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 是否显示详情 */
let showDetail = ref(false);

/** 表格数据源 */
const withDrawalList = ({ pages, where, orders, filters }) => {
  // requestWithdrawalRequestAdminList({
  //   ...pages,
  //   ...where,
  //   ...orders,
  //   ...filters,
  // });
  return requestWithdrawalRequestList({
    ...pages,
    ...where,
    ...orders,
    ...filters,
  });
};

/** 搜索 */
const reload = (where) => {
  tableRef.value?.reload?.({ page: 1, where });
};

/** 打开编辑弹窗 */
const openEdit = (row) => {
  current.value = row ?? null;
  showEdit.value = true;
};

/** 查看数据 */
const showBatch = (row) => {
  console.log(row, 'show');
  // 获取id
  showDetail.value = true;
}

/** 关闭详情 */
const onClose = () => {
  showDetail.value = false;
}
</script>
