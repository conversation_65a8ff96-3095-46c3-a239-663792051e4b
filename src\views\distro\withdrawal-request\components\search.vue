<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ paddingBottom: '2px' }">
    <el-form label-width="100px" @keyup.enter="search" @submit.prevent="">
      <el-row :gutter="8">
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="120px" label="申请时间">
            <el-date-picker  type="datetimerange" start-placeholder="开始时间"
              end-placeholder="结束时间" format="YYYY-MM-DD HH:mm:ss" date-format="YYYY/MM/DD ddd"
              time-format="A hh:mm:ss" />
          </el-form-item>
        </el-col>

        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="状态">
            <el-select v-model="form.status" placeholder="请选择">
              <el-option label="全部" value="" />
              <el-option label="待审核" value="Pending" />
              <el-option label="审核通过" value="Approved" />
              <el-option label="已拒绝" value="Rejected" />
              <el-option label="处理中" value="Processing" />
              <el-option label="已支付" value="Paid" />
            </el-select>
          </el-form-item>
        </el-col>


        <el-col :lg="4" :md="12" :sm="12" :xs="24">
          <el-form-item label="分销商名称">
            <el-input v-model="form.vendorName" placeholder="请输入分销商名称" />
          </el-form-item>
        </el-col>

        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-col>

      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
import { onMounted } from "vue";
import { useFormData } from "@/utils/use-form-data";

/** 定义事件 */
const emit = defineEmits(["search"]);

/** 表单数据 */
const [form, resetFields] = useFormData({
  username: null,
  realName: null,
  roleId: null,
  teamId: null
});

/** 搜索 */
const search = () => {
  emit("search", { ...form });
};

/** 重置 */
const reset = () => {
  resetFields();
  search();
};


/** 更多 */
// const more = ref(false);
// const toggleMore = () => {
//   more.value = !more.value;
// };

onMounted(() => {

});
</script>
