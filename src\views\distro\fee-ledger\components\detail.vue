<template>
  <ele-drawer
    :model-value="visible"
    @update:model-value="updateVisible"
    :title="'佣金记录详情'"
    :size="800"
    style="max-width: 100%;"
    :destroy-on-close="true"
    @closed="close"
  >
    <div v-loading="loading" class="drawer-container">
      <div class="subtitle mt--4"><span>基本信息</span></div>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="总账ID">
          <ele-copyable>{{ detail.ledgerId }}</ele-copyable>
        </el-descriptions-item>
        <el-descriptions-item label="续期记录ID">
          <ele-copyable>{{ detail.renewalId }}</ele-copyable>
        </el-descriptions-item>

        <el-descriptions-item label="总佣金金额">
          {{ detail.totalFee }} 元
        </el-descriptions-item>
        <el-descriptions-item label="结算时间">
          {{ detail.calcTime }}
        </el-descriptions-item>              
        
        <el-descriptions-item label="年卡卡号">
          <ele-copyable v-if="detail.card">{{ detail.card?.cardNumber }}</ele-copyable>
        </el-descriptions-item>
        <el-descriptions-item label="年卡ID">
          <ele-copyable v-if="detail.cardId">{{ detail.cardId }}</ele-copyable>
        </el-descriptions-item>

        <el-descriptions-item label="实体券号">
          <ele-copyable v-if="detail.ticket">{{ detail.ticket?.ticketNumber }}</ele-copyable>
        </el-descriptions-item>
        <el-descriptions-item label="实体券ID">
          <ele-copyable v-if="detail.ticketId">{{ detail.ticketId }}</ele-copyable>
        </el-descriptions-item>        
        
        <el-descriptions-item label="备注" :span="2">
          {{ detail.remark }}
        </el-descriptions-item>

        <el-descriptions-item v-if="detail.errorMsg" label="错误信息" :span="2">
          <pre style="color: #F56C6C;">{{ detail.errorMsg }}</pre>
        </el-descriptions-item>
      </el-descriptions>

      <div class="subtitle mt-4"><span>佣金明细</span></div>
      <ele-pro-table
        ref="tableRef"
        row-key="feeDistroId"
        :columns="columns"
        :datasource="feeDistroList"
        :pagination="true"
        :show-overflow-tooltip="false"
      >
        <template #vendorId="{ row }">
          <ele-copyable>{{ row.vendorId }}</ele-copyable>
        </template>
        <template #status="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusLabel(row.status) }}
          </el-tag>
        </template>
      </ele-pro-table>
    </div>
  </ele-drawer>
</template>

<script setup>
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus/es';
import { requestFeeLedgerDetail } from '@/api/distro/fee-ledger';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['close', 'update:visible']);

const loading = ref(false);

const detail = ref({
  ledgerId: '',
  renewalId: '',
  triggeredByCardNumber: '',
  triggeredByTicketNumber: '',
  calculationTime: '',
  totalFeeAmount: '',
  distributionStatus: '',
  remark: '',
  vendor: null,
  vendorId: '',
  createByUser: null,
  createTime: '',
  errorMsg: ''
});

/** 表格列配置 */
const columns = ref([
  {
    prop: 'vendorId',
    label: '分销商ID',
    align: 'center',
    width: 120,
    slot: 'vendorId'
  },
  {
    prop: 'vendorName',
    label: '分销商名称',
    align: 'left',
    minWidth: 120
  },
  {
    prop: 'feeAmount',
    label: '分配金额',
    align: 'right',
    width: 100
  },
  {
    prop: 'status',
    label: '状态',
    align: 'center',
    width: 100,
    slot: 'status'
  },
  {
    prop: 'settlementTime',
    label: '结算时间',
    align: 'center',
    width: 160
  }
]);


/** 表格数据源 */
const feeDistroList = ({ _pages }) => {
  // 这里应该调用实际的API获取佣金分配明细列表
  // 为了演示，这里返回模拟数据
  return Promise.resolve({
    list: [],
    count: 0
  });
};

/** 加载详情数据 */
async function loadDetail() {
  if (!props.data?.ledgerId) {
    ElMessage.error('获取ID失败');
    return;
  }

  loading.value = true;
  try {
    const result = await requestFeeLedgerDetail({
      ledgerId: props.data.ledgerId
    });
    Object.assign(detail.value, result);
  } catch (error) {
    ElMessage.error(error.message || '获取数据失败');
  } finally {
    loading.value = false;
  }
}

/** 更新visible值 */
const updateVisible = (value) => {
  emit('update:visible', value);
};

/** 监听显示状态 */
watch(
  () => props.visible,
  (value) => {
    if (value) {
      loadDetail();
    }
  }
);

/** 关闭 */
function close() {
  emit('close');
}
</script>

<style scoped lang="scss">
.drawer-container {
  padding: 0 12px;
}

:deep(.el-descriptions__label) {
  font-weight: bold;
}

</style> 