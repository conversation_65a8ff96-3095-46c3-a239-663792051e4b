<template>
  <ele-drawer
    :model-value="visible"
    @update:model-value="updateVisible"
    :title="'佣金记录详情'"
    :size="800"
    style="max-width: 100%;"
    :destroy-on-close="true"
    @closed="close"
  >
    <div v-loading="loading" class="drawer-container">
      <div class="subtitle mt--4"><span>基本信息</span></div>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="总账ID">
          <ele-copyable>{{ detail.ledgerId }}</ele-copyable>
        </el-descriptions-item>
        <el-descriptions-item label="续期记录ID">
          <ele-copyable>{{ detail.renewalId }}</ele-copyable>
        </el-descriptions-item>

        <el-descriptions-item label="总佣金金额">
          {{ detail.totalFee }} 元
        </el-descriptions-item>
        <el-descriptions-item label="结算时间">
          {{ detail.calcTime }}
        </el-descriptions-item>              
        
        <el-descriptions-item label="年卡卡号">
          <ele-copyable v-if="detail.card">{{ detail.card?.cardNumber }}</ele-copyable>
        </el-descriptions-item>
        <el-descriptions-item label="年卡ID">
          <ele-copyable v-if="detail.cardId">{{ detail.cardId }}</ele-copyable>
        </el-descriptions-item>

        <el-descriptions-item label="实体券号">
          <ele-copyable v-if="detail.ticket">{{ detail.ticket?.ticketNumber }}</ele-copyable>
        </el-descriptions-item>
        <el-descriptions-item label="实体券ID">
          <ele-copyable v-if="detail.ticketId">{{ detail.ticketId }}</ele-copyable>
        </el-descriptions-item>        
        
        <el-descriptions-item label="备注" :span="2">
          {{ detail.remark }}
        </el-descriptions-item>

        <el-descriptions-item v-if="detail.errorMsg" label="错误信息" :span="2">
          <pre style="color: #F56C6C;">{{ detail.errorMsg }}</pre>
        </el-descriptions-item>
      </el-descriptions>

      <div class="subtitle mt-4"><span>佣金明细</span></div>
      <ele-data-table
        row-key="userId"
        :columns="columns"
        :data="detail.distroList || []"
      >
        <template #distroId="{ row }">
          <ele-copyable>{{ row.distroId }}</ele-copyable>
        </template>
        <template #ledgerId="{ row }">
          <ele-copyable>{{ row.ledgerId }}</ele-copyable>
        </template>
        <template #productId="{ row }">
          <ele-copyable>{{ row.productId }}</ele-copyable>
        </template>
        <template #ticketId="{ row }">
          <ele-copyable>{{ row.ticketId }}</ele-copyable>
        </template>
        <template #cardId="{ row }">
          <ele-copyable>{{ row.cardId }}</ele-copyable>
        </template>
        <template #vendorId="{ row }">
          <ele-copyable>{{ row.vendorId }}</ele-copyable>
        </template>
        <template #feeRuleId="{ row }">
          <ele-copyable>{{ row.feeRuleId }}</ele-copyable>
        </template>
        <template #feeAmount="{ row }">
          {{ row.feeAmount }} 元
        </template>
        <template #status="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusLabel(row.status) }}
          </el-tag>
        </template>
        <template #withdrawalRequestId="{ row }">
          <ele-copyable v-if="row.withdrawalRequestId">{{ row.withdrawalRequestId }}</ele-copyable>
          <span v-else>-</span>
        </template>
      </ele-data-table>
    </div>
  </ele-drawer>
</template>

<script setup>
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus/es';
import { requestFeeLedgerDetail } from '@/api/distro/fee-ledger';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['close', 'update:visible']);

const loading = ref(false);

const detail = ref({
  ledgerId: '',
  renewalId: '',
  triggeredByCardNumber: '',
  triggeredByTicketNumber: '',
  calculationTime: '',
  totalFeeAmount: '',
  distributionStatus: '',
  remark: '',
  vendor: null,
  vendorId: '',
  createByUser: null,
  createTime: '',
  errorMsg: '',
  distroList: []
});

/** 表格列配置 */
const columns = ref([
  {
    prop: 'distroId',
    label: '分销ID',
    align: 'center',
    width: 120,
    slot: 'distroId'
  },
  {
    prop: 'ledgerId',
    label: '总账ID',
    align: 'center',
    width: 120,
    slot: 'ledgerId'
  },
  {
    prop: 'renewalTime',
    label: '续期时间',
    align: 'center',
    width: 160
  },
  {
    prop: 'settleTime',
    label: '结算时间',
    align: 'center',
    width: 160
  },
  {
    prop: 'productId',
    label: '产品ID',
    align: 'center',
    width: 120,
    slot: 'productId'
  },
  {
    prop: 'productName',
    label: '产品名称',
    align: 'left',
    minWidth: 120
  },
  {
    prop: 'ticketId',
    label: '实体券ID',
    align: 'center',
    width: 120,
    slot: 'ticketId'
  },
  {
    prop: 'ticketNumber',
    label: '实体券号',
    align: 'center',
    width: 140
  },
  {
    prop: 'cardId',
    label: '年卡ID',
    align: 'center',
    width: 120,
    slot: 'cardId'
  },
  {
    prop: 'cardNumber',
    label: '年卡号',
    align: 'center',
    width: 140
  },
  {
    prop: 'vendorId',
    label: '分销商ID',
    align: 'center',
    width: 120,
    slot: 'vendorId'
  },
  {
    prop: 'vendorName',
    label: '分销商名称',
    align: 'left',
    minWidth: 120
  },
  {
    prop: 'vendorLevel',
    label: '分销商层级',
    align: 'center',
    width: 100
  },
  {
    prop: 'feeRuleId',
    label: '费用规则ID',
    align: 'center',
    width: 120,
    slot: 'feeRuleId'
  },
  {
    prop: 'feeRuleName',
    label: '费用规则名称',
    align: 'left',
    minWidth: 140
  },
  {
    prop: 'feeAmount',
    label: '佣金金额',
    align: 'right',
    width: 100,
    slot: 'feeAmount'
  },
  {
    prop: 'status',
    label: '状态',
    align: 'center',
    width: 100,
    slot: 'status'
  },
  {
    prop: 'withdrawalRequestId',
    label: '提现请求ID',
    align: 'center',
    width: 140,
    slot: 'withdrawalRequestId'
  },
  {
    prop: 'createTime',
    label: '创建时间',
    align: 'center',
    width: 160
  }
]);




/** 获取状态类型 */
const getStatusType = (status) => {
  const types = {
    'PendingSettlement': 'warning',
    'Settled': 'success',
    'IncludedInWithdrawal': 'info',
    'Failed': 'danger'
  };
  return types[status] || 'info';
};

/** 获取状态标签 */
const getStatusLabel = (status) => {
  const labels = {
    'PendingSettlement': '待结算',
    'Settled': '已结算',
    'IncludedInWithdrawal': '已提现',
    'Failed': '失败'
  };
  return labels[status] || status;
};

/** 加载详情数据 */
async function loadDetail() {
  if (!props.data?.ledgerId) {
    ElMessage.error('获取ID失败');
    return;
  }

  loading.value = true;
  try {
    const result = await requestFeeLedgerDetail({
      ledgerId: props.data.ledgerId
    });
    Object.assign(detail.value, result);
  } catch (error) {
    ElMessage.error(error.message || '获取数据失败');
  } finally {
    loading.value = false;
  }
}

/** 更新visible值 */
const updateVisible = (value) => {
  emit('update:visible', value);
};

/** 监听显示状态 */
watch(
  () => props.visible,
  (value) => {
    if (value) {
      loadDetail();
    }
  }
);

/** 关闭 */
function close() {
  emit('close');
}
</script>

<style scoped lang="scss">
.drawer-container {
  padding: 0 12px;
}

:deep(.el-descriptions__label) {
  font-weight: bold;
}

</style> 